import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';

// Import all available UI components
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Checkbox } from './ui/checkbox';
import { Switch } from './ui/switch';
import { Slider } from './ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Progress } from './ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Separator } from './ui/separator';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { FileDropZone } from './ui/file-drop-zone';
import { ConvertedFilesDisplay } from './ui/converted-files-display';
import { ConversionProgress } from './ui/conversion-progress';
import { Badge } from './ui/badge';
import { Label } from './ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Toast } from './ui/toast';
import { Toaster } from './ui/toaster';
import { useToast } from '../hooks/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from './ui/sheet';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './ui/command';

// Import Lucide icons
import * as LucideIcons from 'lucide-react';
import { logger } from '../utils/logger';

const BlueprintRenderer = ({ config, utilityId, onExecute }) => {
  const [formData, setFormData] = useState({});
  const [files, setFiles] = useState([]);
  const [convertedFiles, setConvertedFiles] = useState([]);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [error, setError] = useState(null);

  // Check if UI field exists and is not empty
  if (!config.ui || config.ui.trim() === '') {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-8">
        <div className="text-center">
          <div className="text-6xl mb-4">🚧</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No UI Layout Found
          </h3>
          <p className="text-gray-600">
            This app doesn't have a UI configuration yet.
          </p>
        </div>
      </div>
    );
  }

  // Component mapping for dynamic rendering
  const componentMap = {
    // Layout components
    'div': 'div',
    'section': 'section',
    'article': 'article',
    'header': 'header',
    'footer': 'footer',
    'main': 'main',

    // UI components
    'Button': Button,
    'Card': Card,
    'CardContent': CardContent,
    'CardDescription': CardDescription,
    'CardHeader': CardHeader,
    'CardTitle': CardTitle,
    'Input': Input,
    'Textarea': Textarea,
    'Checkbox': Checkbox,
    'Switch': Switch,
    'Slider': Slider,
    'Select': Select,
    'SelectContent': SelectContent,
    'SelectItem': SelectItem,
    'SelectTrigger': SelectTrigger,
    'SelectValue': SelectValue,
    'Progress': Progress,
    'Tabs': Tabs,
    'TabsContent': TabsContent,
    'TabsList': TabsList,
    'TabsTrigger': TabsTrigger,
    'Separator': Separator,
    'Alert': Alert,
    'AlertDescription': AlertDescription,
    'AlertTitle': AlertTitle,
    'Badge': Badge,
    'Label': Label,
    'Accordion': Accordion,
    'AccordionContent': AccordionContent,
    'AccordionItem': AccordionItem,
    'AccordionTrigger': AccordionTrigger,
    'Avatar': Avatar,
    'AvatarFallback': AvatarFallback,
    'AvatarImage': AvatarImage,
    'Toast': Toast,
    'Toaster': Toaster,
    'Dialog': Dialog,
    'DialogContent': DialogContent,
    'DialogDescription': DialogDescription,
    'DialogHeader': DialogHeader,
    'DialogTitle': DialogTitle,
    'DialogTrigger': DialogTrigger,
    'DropdownMenu': DropdownMenu,
    'DropdownMenuContent': DropdownMenuContent,
    'DropdownMenuItem': DropdownMenuItem,
    'DropdownMenuTrigger': DropdownMenuTrigger,
    'Popover': Popover,
    'PopoverContent': PopoverContent,
    'PopoverTrigger': PopoverTrigger,
    'Tooltip': Tooltip,
    'TooltipContent': TooltipContent,
    'TooltipProvider': TooltipProvider,
    'TooltipTrigger': TooltipTrigger,
    'Sheet': Sheet,
    'SheetContent': SheetContent,
    'SheetDescription': SheetDescription,
    'SheetHeader': SheetHeader,
    'SheetTitle': SheetTitle,
    'SheetTrigger': SheetTrigger,
    'Table': Table,
    'TableBody': TableBody,
    'TableCell': TableCell,
    'TableHead': TableHead,
    'TableHeader': TableHeader,
    'TableRow': TableRow,
    'Command': Command,
    'CommandEmpty': CommandEmpty,
    'CommandGroup': CommandGroup,
    'CommandInput': CommandInput,
    'CommandItem': CommandItem,
    'CommandList': CommandList,

    // File conversion components
    'FileDropZone': FileDropZone,
    'ConvertedFilesDisplay': ConvertedFilesDisplay,
    'ConversionProgress': ConversionProgress,

    // Motion components
    'motion.div': motion.div,
    'motion.section': motion.section,

    // Text elements
    'h1': 'h1',
    'h2': 'h2',
    'h3': 'h3',
    'h4': 'h4',
    'h5': 'h5',
    'h6': 'h6',
    'p': 'p',
    'span': 'span',
    'label': 'label',

    // Icons (all Lucide icons)
    ...LucideIcons
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      setError('Please select files to convert');
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Prepare file data for the utility
      const fileData = files.map(file => ({
        filename: file.name,
        content: file // For now, pass the file object
      }));

      // Execute the utility
      const result = await onExecute(fileData, formData);
      
      if (result.status === 'success') {
        setConvertedFiles([{
          filename: result.filename,
          filePath: result.file_path,
          rowsConverted: result.rows_converted,
          size: result.size
        }]);
        setConversionProgress(100);
      } else {
        setError(result.message || 'Conversion failed');
      }
    } catch (err) {
      setError(err.message || 'An error occurred during conversion');
    } finally {
      setIsConverting(false);
    }
  };

  const handleOpenFolder = async () => {
    if (window.electronAPI?.openFolder) {
      try {
        const folderPath = process.env.HOME ? `${process.env.HOME}/converted` : './converted';
        await window.electronAPI.openFolder(folderPath);
      } catch (error) {
        logger.error('Failed to open folder:', error);
      }
    }
  };

  // Function to safely evaluate React JSX code
  const evaluateReactCode = (uiCode) => {
    try {
      // Create a safe evaluation context with all available components and hooks
      const context = {
        React,
        useState,
        useMemo,
        motion,
        // All UI components
        ...componentMap,
        // Context variables and functions
        formData,
        setFormData,
        files,
        setFiles,
        convertedFiles,
        setConvertedFiles,
        isConverting,
        conversionProgress,
        error,
        handleConvert,
        handleOpenFolder,
        utilityId,
        config,
        useToast
      };

      // Create a function that returns the JSX
      const functionBody = `
        const { ${Object.keys(context).join(', ')} } = this;
        return (${uiCode});
      `;

      const renderFunction = new Function(functionBody);
      return renderFunction.call(context);
    } catch (error) {
      logger.error('Error evaluating React code:', error);
      throw error;
    }
  };

  // Enhanced context for the UI components
  const uiContext = {
    formData,
    setFormData,
    files,
    setFiles,
    convertedFiles,
    setConvertedFiles,
    isConverting,
    conversionProgress,
    error,
    handleConvert,
    handleOpenFolder,
    utilityId,
    config
  };

  try {
    // If UI field exists and has content, render it dynamically
    if (config.ui && config.ui.trim() !== '') {
      return (
        <div className="p-6 max-w-7xl mx-auto">
          <Toaster />
          {evaluateReactCode(config.ui)}
        </div>
      );
    }

    // Fallback: Show example UI for file conversion when no UI is defined
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <Toaster />
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Dynamic UI Renderer
          </h2>
          <p className="text-gray-600">
            This is the default UI. The app can define custom UI in config.json 'ui' field.
          </p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-blue-800 mb-2">Example File Converter UI</h3>
          <p className="text-sm text-blue-700">
            This demonstrates the available components for building file conversion apps.
          </p>
        </div>

        {/* Example UI for file conversion */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Input Files</CardTitle>
              <CardDescription>
                Drop your files here to convert them
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileDropZone
                onFilesChange={setFiles}
                accept=".json,.csv,.txt"
                multiple={true}
                maxFiles={10}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Converted Files</CardTitle>
              <CardDescription>
                Your converted files will appear here
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ConvertedFilesDisplay
                files={convertedFiles}
                onOpenFolder={handleOpenFolder}
              />
            </CardContent>
          </Card>
        </div>

        <div className="mt-6">
          <ConversionProgress
            isConverting={isConverting}
            progress={conversionProgress}
            error={error}
          />
        </div>

        <div className="mt-6 flex justify-center">
          <Button
            onClick={handleConvert}
            disabled={isConverting || files.length === 0}
            size="lg"
            className="px-8"
          >
            {isConverting ? 'Converting...' : 'Convert Files'}
          </Button>
        </div>
      </div>
    );
  } catch (error) {
    return (
      <div className="p-6 text-center">
        <Toaster />
        <div className="text-red-500 mb-4">
          <LucideIcons.AlertCircle className="mx-auto h-12 w-12" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          UI Rendering Error
        </h3>
        <p className="text-gray-600 mb-4">
          Failed to render the UI: {error.message}
        </p>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-left">
          <h4 className="font-medium text-red-800 mb-2">Error Details:</h4>
          <pre className="text-sm text-red-700 whitespace-pre-wrap">
            {error.stack || error.message}
          </pre>
        </div>
      </div>
    );
  }
};

export default BlueprintRenderer;
