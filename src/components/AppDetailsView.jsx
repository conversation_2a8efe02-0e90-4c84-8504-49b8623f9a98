import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Star, Download, Share, MoreHorizontal, CheckCircle, Loader2 } from 'lucide-react';
import { PageWrapper } from './ui/page-wrapper';
import { MacOSButton } from './ui/macos-button';
import { MacOSCard, MacOSCardContent } from './ui/macos-card';
import { Typography } from '../utils/typography';
import { useStoreData } from '../contexts/StoreDataContext';
import { logger } from '../utils/logger';

const AppDetailsView = () => {
  const { appId } = useParams();
  const navigate = useNavigate();
  const { trendingApps, popularApps, newApps, recommendedApps, installedUtilities } = useStoreData();
  const [app, setApp] = useState(null);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installationStatus, setInstallationStatus] = useState(null);

  // API configuration
  const API_BASE_URL = 'http://localhost:8000';
  const USER_ID = 'user_test_123';

  useEffect(() => {
    // Find the app in all sections
    const allApps = [...trendingApps, ...popularApps, ...newApps, ...recommendedApps];
    const foundApp = allApps.find(a => a.app_id === appId || a.id === appId);
    setApp(foundApp);
  }, [appId, trendingApps, popularApps, newApps, recommendedApps]);

  const isInstalled = installedUtilities.some(util => util.id === appId);

  const handleInstall = async () => {
    if (!app || isInstalled) return;

    setIsInstalling(true);
    setInstallationStatus(null);

    try {
      // Call install API
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/${app.app_id}/install`, {
        method: 'POST',
        headers: {
          'user-id': USER_ID,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('Failed to record installation');

      // Install the app using Electron API
      const result = await window.electronAPI.installUtility(app.installer_url);
      
      if (result.success) {
        setInstallationStatus('success');
        // Refresh installed utilities
        if (window.electronAPI.getInstalledUtilities) {
          await window.electronAPI.getInstalledUtilities();
        }
      } else {
        throw new Error(result.error || 'Installation failed');
      }
    } catch (error) {
      logger.error('Installation failed:', error);
      setInstallationStatus('error');
    } finally {
      setIsInstalling(false);
    }
  };

  if (!app) {
    return (
      <PageWrapper>
        <div className="p-8 h-full flex items-center justify-center">
          <div className="text-center">
            <h2 className={Typography.title2}>App Not Found</h2>
            <p className={Typography.bodySecondary}>The requested app could not be found.</p>
            <MacOSButton onClick={() => navigate('/store')} className="mt-4">
              Back to Store
            </MacOSButton>
          </div>
        </div>
      </PageWrapper>
    );
  }

  // Dummy preview images for now
  const previewImages = [
    'https://via.placeholder.com/600x400/007AFF/FFFFFF?text=Preview+1',
    'https://via.placeholder.com/600x400/34C759/FFFFFF?text=Preview+2',
    'https://via.placeholder.com/600x400/FF9500/FFFFFF?text=Preview+3'
  ];

  // Dummy related apps
  const relatedApps = trendingApps.slice(0, 3);

  return (
    <PageWrapper>
      <div className="p-8 h-full overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center space-x-4 mb-8">
            <MacOSButton
              variant="secondary"
              onClick={() => navigate('/store')}
              className="rounded-full"
            >
              <ArrowLeft className="h-4 w-4" />
            </MacOSButton>
            <h1 className={Typography.largeTitle}>App Details</h1>
          </div>

          {/* App Info Section */}
          <MacOSCard className="mb-8">
            <MacOSCardContent className="p-6">
              <div className="flex items-start space-x-6">
                {/* App Icon */}
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-2xl font-bold">
                    {app.name?.charAt(0) || 'A'}
                  </span>
                </div>

                {/* App Details */}
                <div className="flex-1">
                  <h2 className={Typography.title1}>{app.name}</h2>
                  <p className={Typography.body}>{app.description}</p>
                  <div className="flex items-center space-x-4 mt-4">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className={Typography.caption}>4.8</span>
                    </div>
                    <span className={Typography.caption}>Productivity</span>
                    <span className={Typography.caption}>Free</span>
                  </div>
                </div>

                {/* Install Button */}
                <div className="flex-shrink-0">
                  <MacOSButton
                    onClick={handleInstall}
                    disabled={isInstalling || isInstalled}
                    variant={isInstalled ? "secondary" : "primary"}
                    size="lg"
                    className="min-w-[100px]"
                  >
                    {isInstalling ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : isInstalled ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Installed
                      </>
                    ) : (
                      'Get'
                    )}
                  </MacOSButton>
                </div>
              </div>
            </MacOSCardContent>
          </MacOSCard>

          {/* Preview Section */}
          <div className="mb-8">
            <h3 className={Typography.title2}>Preview</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {previewImages.map((image, index) => (
                <motion.div
                  key={index}
                  className="aspect-video bg-macos-surface rounded-xl overflow-hidden border border-macos-border"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <img
                    src={image}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </motion.div>
              ))}
            </div>
          </div>

          {/* Related Apps Section */}
          <div>
            <h3 className={Typography.title2}>You Might Also Like</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {relatedApps.map((relatedApp) => (
                <MacOSCard
                  key={relatedApp.app_id}
                  className="cursor-pointer hover:bg-macos-elevated transition-colors"
                  onClick={() => navigate(`/store/app/${relatedApp.app_id}`)}
                >
                  <MacOSCardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-sm font-bold">
                          {relatedApp.name?.charAt(0) || 'A'}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className={`${Typography.body} font-medium truncate`}>
                          {relatedApp.name}
                        </h4>
                        <p className={`${Typography.caption} truncate`}>
                          {relatedApp.description}
                        </p>
                      </div>
                    </div>
                  </MacOSCardContent>
                </MacOSCard>
              ))}
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default AppDetailsView;
