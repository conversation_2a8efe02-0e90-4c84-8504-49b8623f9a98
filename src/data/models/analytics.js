const { getDatabase } = require('../db');
const { log } = require('../../common/log');
const DateUtils = require('../../common/dateUtils');

/**
 * Analytics model - handles CRUD operations for usage analytics
 */
class AnalyticsModel {
  constructor() {
    this.db = getDatabase().analytics;
    this.logger = log.scope('AnalyticsModel');
  }

  /**
   * Track an analytics event
   */
  async trackEvent(eventData) {
    try {
      // Validate required fields
      if (!eventData.event_type) {
        throw new Error('Missing required field: event_type');
      }

      const analyticsEntry = {
        event_type: eventData.event_type,
        app_id: eventData.app_id || null,
        user_id: eventData.user_id || 'user_test_123',
        session_id: eventData.session_id || this.generateSessionId(),
        timestamp: DateUtils.toISOString(),
        properties: eventData.properties || {},
        metadata: {
          platform: process.platform,
          app_version: eventData.app_version || '0.1.0',
          electron_version: process.versions.electron || 'unknown',
          ...eventData.metadata
        }
      };

      const result = await this.db.insert(analyticsEntry);
      this.logger.info(`Tracked event: ${eventData.event_type}`);
      return result;
    } catch (error) {
      this.logger.error('Error tracking analytics event:', error);
      throw error;
    }
  }

  /**
   * Track app installation
   */
  async trackAppInstall(appId, properties = {}) {
    return this.trackEvent({
      event_type: 'app_install',
      app_id: appId,
      properties: {
        ...properties,
        timestamp: DateUtils.toISOString()
      }
    });
  }

  /**
   * Track app uninstallation
   */
  async trackAppUninstall(appId, properties = {}) {
    return this.trackEvent({
      event_type: 'app_uninstall',
      app_id: appId,
      properties: {
        ...properties,
        timestamp: DateUtils.toISOString()
      }
    });
  }

  /**
   * Track app execution
   */
  async trackAppRun(appId, properties = {}) {
    return this.trackEvent({
      event_type: 'app_run',
      app_id: appId,
      properties: {
        ...properties,
        timestamp: DateUtils.toISOString()
      }
    });
  }

  /**
   * Get analytics events with filtering
   */
  async getEvents(options = {}) {
    try {
      const {
        event_type,
        app_id,
        user_id,
        start_date,
        end_date,
        limit = 100,
        skip = 0,
        sortBy = 'timestamp',
        sortOrder = -1
      } = options;

      let query = {};
      
      if (event_type) query.event_type = event_type;
      if (app_id) query.app_id = app_id;
      if (user_id) query.user_id = user_id;
      
      if (start_date || end_date) {
        query.timestamp = {};
        if (start_date) query.timestamp.$gte = start_date;
        if (end_date) query.timestamp.$lte = end_date;
      }

      const events = await this.db.find(query)
        .sort({ [sortBy]: sortOrder })
        .skip(skip)
        .limit(limit);

      return events;
    } catch (error) {
      this.logger.error('Error getting analytics events:', error);
      throw error;
    }
  }

  /**
   * Get events by type
   */
  async getEventsByType(eventType, limit = 50) {
    try {
      const events = await this.db.find({ event_type: eventType })
        .sort({ timestamp: -1 })
        .limit(limit);
      return events;
    } catch (error) {
      this.logger.error(`Error getting events by type ${eventType}:`, error);
      throw error;
    }
  }

  /**
   * Get events by app ID
   */
  async getEventsByAppId(appId, limit = 50) {
    try {
      const events = await this.db.find({ app_id: appId })
        .sort({ timestamp: -1 })
        .limit(limit);
      return events;
    } catch (error) {
      this.logger.error(`Error getting events for app ${appId}:`, error);
      throw error;
    }
  }

  /**
   * Get analytics summary
   */
  async getAnalyticsSummary(timeRange = '7d') {
    try {
      const now = new Date();
      let startDate;

      switch (timeRange) {
        case '1d':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      const query = { timestamp: { $gte: startDate.toISOString() } };

      const totalEvents = await this.db.count(query);
      const appInstalls = await this.db.count({ ...query, event_type: 'app_install' });
      const appRuns = await this.db.count({ ...query, event_type: 'app_run' });
      const appUninstalls = await this.db.count({ ...query, event_type: 'app_uninstall' });

      // Get most used apps
      const appRunEvents = await this.db.find({ ...query, event_type: 'app_run' });
      const appUsage = {};
      appRunEvents.forEach(event => {
        if (event.app_id) {
          appUsage[event.app_id] = (appUsage[event.app_id] || 0) + 1;
        }
      });

      const mostUsedApps = Object.entries(appUsage)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([app_id, count]) => ({ app_id, count }));

      return {
        time_range: timeRange,
        total_events: totalEvents,
        app_installs: appInstalls,
        app_runs: appRuns,
        app_uninstalls: appUninstalls,
        most_used_apps: mostUsedApps
      };
    } catch (error) {
      this.logger.error('Error getting analytics summary:', error);
      throw error;
    }
  }

  /**
   * Get daily analytics for a date range
   */
  async getDailyAnalytics(days = 7) {
    try {
      const results = [];
      const now = new Date();

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        date.setHours(0, 0, 0, 0);
        
        const nextDate = new Date(date);
        nextDate.setDate(nextDate.getDate() + 1);

        const query = {
          timestamp: {
            $gte: date.toISOString(),
            $lt: nextDate.toISOString()
          }
        };

        const totalEvents = await this.db.count(query);
        const appInstalls = await this.db.count({ ...query, event_type: 'app_install' });
        const appRuns = await this.db.count({ ...query, event_type: 'app_run' });

        results.push({
          date: date.toISOString().split('T')[0],
          total_events: totalEvents,
          app_installs: appInstalls,
          app_runs: appRuns
        });
      }

      return results;
    } catch (error) {
      this.logger.error('Error getting daily analytics:', error);
      throw error;
    }
  }

  /**
   * Delete old analytics data
   */
  async deleteOldAnalytics(daysOld = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await this.db.remove({
        timestamp: { $lt: cutoffDate.toISOString() }
      }, { multi: true });

      this.logger.info(`Deleted ${result} analytics records older than ${daysOld} days`);
      return result;
    } catch (error) {
      this.logger.error('Error deleting old analytics:', error);
      throw error;
    }
  }

  /**
   * Get analytics statistics
   */
  async getAnalyticsStats() {
    try {
      const totalEvents = await this.db.count({});
      
      // Get counts by event type
      const installEvents = await this.db.count({ event_type: 'app_install' });
      const runEvents = await this.db.count({ event_type: 'app_run' });
      const uninstallEvents = await this.db.count({ event_type: 'app_uninstall' });
      const errorEvents = await this.db.count({ event_type: 'app_error' });

      // Get unique apps count
      const uniqueApps = await this.db.find({ app_id: { $ne: null } })
        .then(events => {
          const appIds = new Set(events.map(e => e.app_id));
          return appIds.size;
        });

      return {
        total_events: totalEvents,
        unique_apps: uniqueApps,
        by_type: {
          app_install: installEvents,
          app_run: runEvents,
          app_uninstall: uninstallEvents,
          app_error: errorEvents
        }
      };
    } catch (error) {
      this.logger.error('Error getting analytics statistics:', error);
      throw error;
    }
  }

  /**
   * Generate session ID
   */
  generateSessionId() {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Export analytics data
   */
  async exportAnalytics(options = {}) {
    try {
      const events = await this.getEvents({ ...options, limit: 10000 });
      return JSON.stringify(events, null, 2);
    } catch (error) {
      this.logger.error('Error exporting analytics:', error);
      throw error;
    }
  }
}

module.exports = AnalyticsModel;
