const { getDatabase } = require('../db');
const { log } = require('../../common/log');

/**
 * Settings model - handles CRUD operations for application settings
 */
class SettingsModel {
  constructor() {
    this.db = getDatabase().settings;
    this.logger = log.scope('SettingsModel');
  }

  /**
   * Get setting by key
   */
  async getSetting(key, defaultValue = null) {
    try {
      const setting = await this.db.findOne({ key });
      return setting ? setting.value : defaultValue;
    } catch (error) {
      this.logger.error(`Error getting setting ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Set setting value
   */
  async setSetting(key, value, description = '') {
    try {
      // Determine value type
      let type = typeof value;
      if (Array.isArray(value)) {
        type = 'array';
      } else if (value === null) {
        type = 'null';
      }

      const settingData = {
        key,
        value,
        type,
        description
      };

      const result = await this.db.update(
        { key },
        { $set: settingData },
        { upsert: true, returnUpdatedDocs: true }
      );

      this.logger.info(`Setting updated: ${key}`);
      return result.affectedDocuments ? result.affectedDocuments[0] : result;
    } catch (error) {
      this.logger.error(`Error setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get all settings
   */
  async getAllSettings() {
    try {
      const settings = await this.db.find({}).sort({ key: 1 });
      return settings;
    } catch (error) {
      this.logger.error('Error getting all settings:', error);
      throw error;
    }
  }

  /**
   * Get settings as key-value object
   */
  async getSettingsObject() {
    try {
      const settings = await this.getAllSettings();
      const settingsObj = {};
      
      settings.forEach(setting => {
        settingsObj[setting.key] = setting.value;
      });

      return settingsObj;
    } catch (error) {
      this.logger.error('Error getting settings object:', error);
      throw error;
    }
  }

  /**
   * Delete setting by key
   */
  async deleteSetting(key) {
    try {
      const result = await this.db.remove({ key });
      
      if (result === 0) {
        throw new Error(`Setting with key ${key} not found`);
      }

      this.logger.info(`Setting deleted: ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`Error deleting setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * Check if setting exists
   */
  async hasSetting(key) {
    try {
      const setting = await this.db.findOne({ key });
      return !!setting;
    } catch (error) {
      this.logger.error(`Error checking setting ${key}:`, error);
      return false;
    }
  }

  /**
   * Get settings by type
   */
  async getSettingsByType(type) {
    try {
      const settings = await this.db.find({ type }).sort({ key: 1 });
      return settings;
    } catch (error) {
      this.logger.error(`Error getting settings by type ${type}:`, error);
      throw error;
    }
  }

  /**
   * Search settings by key pattern
   */
  async searchSettings(pattern) {
    try {
      const regex = new RegExp(pattern, 'i');
      const settings = await this.db.find({
        $or: [
          { key: regex },
          { description: regex }
        ]
      }).sort({ key: 1 });

      return settings;
    } catch (error) {
      this.logger.error(`Error searching settings with pattern "${pattern}":`, error);
      throw error;
    }
  }

  /**
   * Initialize default settings
   */
  async initializeDefaults() {
    try {
      const defaults = {
        'user_preferences': {
          theme: 'light',
          auto_update: true,
          analytics_enabled: true,
          notifications_enabled: true,
          default_download_path: null
        },
        'app_config': {
          max_concurrent_downloads: 3,
          auto_cleanup_temp_files: true,
          log_retention_days: 30,
          cache_size_mb: 100
        },
        'api_settings': {
          base_url: 'http://127.0.0.1:8000',
          timeout_ms: 30000,
          retry_attempts: 3
        },
        'debug_settings': {
          debug_mode: false,
          verbose_logging: false,
          performance_monitoring: false
        }
      };

      for (const [key, value] of Object.entries(defaults)) {
        const exists = await this.hasSetting(key);
        if (!exists) {
          await this.setSetting(key, value, `Default ${key.replace('_', ' ')} configuration`);
        }
      }

      this.logger.success('Default settings initialized');
    } catch (error) {
      this.logger.error('Error initializing default settings:', error);
      throw error;
    }
  }

  /**
   * Get user preferences
   */
  async getUserPreferences() {
    return this.getSetting('user_preferences', {
      theme: 'light',
      auto_update: true,
      analytics_enabled: true,
      notifications_enabled: true,
      default_download_path: null
    });
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(preferences) {
    const currentPrefs = await this.getUserPreferences();
    const updatedPrefs = { ...currentPrefs, ...preferences };
    return this.setSetting('user_preferences', updatedPrefs, 'User preference settings');
  }

  /**
   * Get app configuration
   */
  async getAppConfig() {
    return this.getSetting('app_config', {
      max_concurrent_downloads: 3,
      auto_cleanup_temp_files: true,
      log_retention_days: 30,
      cache_size_mb: 100
    });
  }

  /**
   * Update app configuration
   */
  async updateAppConfig(config) {
    const currentConfig = await this.getAppConfig();
    const updatedConfig = { ...currentConfig, ...config };
    return this.setSetting('app_config', updatedConfig, 'Application configuration settings');
  }

  /**
   * Get API settings
   */
  async getApiSettings() {
    return this.getSetting('api_settings', {
      base_url: 'http://127.0.0.1:8000',
      timeout_ms: 30000,
      retry_attempts: 3
    });
  }

  /**
   * Update API settings
   */
  async updateApiSettings(settings) {
    const currentSettings = await this.getApiSettings();
    const updatedSettings = { ...currentSettings, ...settings };
    return this.setSetting('api_settings', updatedSettings, 'API configuration settings');
  }

  /**
   * Get debug settings
   */
  async getDebugSettings() {
    return this.getSetting('debug_settings', {
      debug_mode: false,
      verbose_logging: false,
      performance_monitoring: false
    });
  }

  /**
   * Update debug settings
   */
  async updateDebugSettings(settings) {
    const currentSettings = await this.getDebugSettings();
    const updatedSettings = { ...currentSettings, ...settings };
    return this.setSetting('debug_settings', updatedSettings, 'Debug configuration settings');
  }

  /**
   * Export all settings to JSON
   */
  async exportSettings() {
    try {
      const settings = await this.getSettingsObject();
      return JSON.stringify(settings, null, 2);
    } catch (error) {
      this.logger.error('Error exporting settings:', error);
      throw error;
    }
  }

  /**
   * Import settings from JSON
   */
  async importSettings(jsonString) {
    try {
      const settings = JSON.parse(jsonString);
      
      for (const [key, value] of Object.entries(settings)) {
        await this.setSetting(key, value, 'Imported setting');
      }

      this.logger.success(`Imported ${Object.keys(settings).length} settings`);
      return Object.keys(settings).length;
    } catch (error) {
      this.logger.error('Error importing settings:', error);
      throw error;
    }
  }

  /**
   * Reset all settings to defaults
   */
  async resetToDefaults() {
    try {
      // Clear all existing settings
      await this.db.remove({}, { multi: true });
      
      // Initialize defaults
      await this.initializeDefaults();
      
      this.logger.warn('All settings reset to defaults');
    } catch (error) {
      this.logger.error('Error resetting settings to defaults:', error);
      throw error;
    }
  }
}

module.exports = SettingsModel;
