const { getDatabase } = require('../db');
const { log } = require('../../common/log');
const DateUtils = require('../../common/dateUtils');

/**
 * Logs model - handles CRUD operations for application logs
 */
class LogsModel {
  constructor() {
    this.db = getDatabase().logs;
    this.logger = log.scope('LogsModel');
  }

  /**
   * Create a new log entry
   */
  async createLog(logData) {
    try {
      // Validate required fields
      if (!logData.type || !logData.message) {
        throw new Error('Missing required fields: type, message');
      }

      const logEntry = {
        type: logData.type,
        level: logData.level || 'info',
        message: logData.message,
        app_id: logData.app_id || null,
        details: logData.details || {},
        user_id: logData.user_id || 'user_test_123',
        session_id: logData.session_id || null,
        timestamp: DateUtils.toISOString()
      };

      const result = await this.db.insert(logEntry);
      return result;
    } catch (error) {
      this.logger.error('Error creating log:', error);
      throw error;
    }
  }

  /**
   * Get all logs with optional filtering
   */
  async getAllLogs(options = {}) {
    try {
      const {
        type,
        level,
        app_id,
        limit = 100,
        skip = 0,
        sortBy = 'createdAt',
        sortOrder = -1
      } = options;

      let query = {};
      
      if (type) query.type = type;
      if (level) query.level = level;
      if (app_id) query.app_id = app_id;

      const logs = await this.db.find(query)
        .sort({ [sortBy]: sortOrder })
        .skip(skip)
        .limit(limit);

      return logs;
    } catch (error) {
      this.logger.error('Error getting logs:', error);
      throw error;
    }
  }

  /**
   * Get logs by type
   */
  async getLogsByType(type, limit = 50) {
    try {
      const logs = await this.db.find({ type })
        .sort({ createdAt: -1 })
        .limit(limit);
      return logs;
    } catch (error) {
      this.logger.error(`Error getting logs by type ${type}:`, error);
      throw error;
    }
  }

  /**
   * Get logs by app ID
   */
  async getLogsByAppId(appId, limit = 50) {
    try {
      const logs = await this.db.find({ app_id: appId })
        .sort({ createdAt: -1 })
        .limit(limit);
      return logs;
    } catch (error) {
      this.logger.error(`Error getting logs for app ${appId}:`, error);
      throw error;
    }
  }

  /**
   * Get logs by level
   */
  async getLogsByLevel(level, limit = 50) {
    try {
      const logs = await this.db.find({ level })
        .sort({ createdAt: -1 })
        .limit(limit);
      return logs;
    } catch (error) {
      this.logger.error(`Error getting logs by level ${level}:`, error);
      throw error;
    }
  }

  /**
   * Get recent logs
   */
  async getRecentLogs(limit = 20) {
    try {
      const logs = await this.db.find({})
        .sort({ createdAt: -1 })
        .limit(limit);
      return logs;
    } catch (error) {
      this.logger.error('Error getting recent logs:', error);
      throw error;
    }
  }

  /**
   * Get logs within date range
   */
  async getLogsByDateRange(startDate, endDate, limit = 100) {
    try {
      const logs = await this.db.find({
        createdAt: {
          $gte: startDate,
          $lte: endDate
        }
      })
      .sort({ createdAt: -1 })
      .limit(limit);

      return logs;
    } catch (error) {
      this.logger.error('Error getting logs by date range:', error);
      throw error;
    }
  }

  /**
   * Search logs by message content
   */
  async searchLogs(query, limit = 50) {
    try {
      const regex = new RegExp(query, 'i');
      const logs = await this.db.find({
        $or: [
          { message: regex },
          { 'details.error': regex }
        ]
      })
      .sort({ createdAt: -1 })
      .limit(limit);

      return logs;
    } catch (error) {
      this.logger.error(`Error searching logs with query "${query}":`, error);
      throw error;
    }
  }

  /**
   * Delete log by ID
   */
  async deleteLog(logId) {
    try {
      const result = await this.db.remove({ _id: logId });
      
      if (result === 0) {
        throw new Error(`Log with ID ${logId} not found`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error deleting log ${logId}:`, error);
      throw error;
    }
  }

  /**
   * Delete logs older than specified days
   */
  async deleteOldLogs(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await this.db.remove({
        createdAt: { $lt: cutoffDate.toISOString() }
      }, { multi: true });

      this.logger.info(`Deleted ${result} logs older than ${daysOld} days`);
      return result;
    } catch (error) {
      this.logger.error(`Error deleting old logs:`, error);
      throw error;
    }
  }

  /**
   * Get log statistics
   */
  async getLogStats() {
    try {
      const totalLogs = await this.db.count({});
      
      // Get counts by level
      const errorLogs = await this.db.count({ level: 'error' });
      const warnLogs = await this.db.count({ level: 'warn' });
      const infoLogs = await this.db.count({ level: 'info' });
      const debugLogs = await this.db.count({ level: 'debug' });

      // Get counts by type
      const installLogs = await this.db.count({ type: 'app_install' });
      const runLogs = await this.db.count({ type: 'app_run' });
      const errorTypeLogs = await this.db.count({ type: 'app_error' });

      return {
        total: totalLogs,
        by_level: {
          error: errorLogs,
          warn: warnLogs,
          info: infoLogs,
          debug: debugLogs
        },
        by_type: {
          app_install: installLogs,
          app_run: runLogs,
          app_error: errorTypeLogs
        }
      };
    } catch (error) {
      this.logger.error('Error getting log statistics:', error);
      throw error;
    }
  }

  /**
   * Log app installation
   */
  async logAppInstall(appId, appName, details = {}) {
    return this.createLog({
      type: 'app_install',
      level: 'info',
      message: `App installed: ${appName}`,
      app_id: appId,
      details
    });
  }

  /**
   * Log app uninstallation
   */
  async logAppUninstall(appId, appName, details = {}) {
    return this.createLog({
      type: 'app_uninstall',
      level: 'info',
      message: `App uninstalled: ${appName}`,
      app_id: appId,
      details
    });
  }

  /**
   * Log app execution
   */
  async logAppRun(appId, appName, success = true, details = {}) {
    return this.createLog({
      type: 'app_run',
      level: success ? 'info' : 'error',
      message: `App ${success ? 'executed successfully' : 'execution failed'}: ${appName}`,
      app_id: appId,
      details: {
        ...details,
        success
      }
    });
  }

  /**
   * Log app error
   */
  async logAppError(appId, appName, error, details = {}) {
    return this.createLog({
      type: 'app_error',
      level: 'error',
      message: `App error: ${appName} - ${error.message}`,
      app_id: appId,
      details: {
        ...details,
        error: error.message,
        stack: error.stack
      }
    });
  }

  /**
   * Log system error
   */
  async logSystemError(error, details = {}) {
    return this.createLog({
      type: 'system_error',
      level: 'error',
      message: `System error: ${error.message}`,
      details: {
        ...details,
        error: error.message,
        stack: error.stack
      }
    });
  }
}

module.exports = LogsModel;
