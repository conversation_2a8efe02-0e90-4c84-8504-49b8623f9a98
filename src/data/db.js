const Datastore = require('nedb-promises');
const { app } = require('electron');
const path = require('path');
const { log } = require('../common/log');

/**
 * NeDB database initialization and management
 * Handles all database connections and setup
 */
class Database {
  constructor() {
    this.dbPath = app.getPath('userData');
    this.databases = {};
    this.logger = log.scope('Database');
    
    this.initializeDatabases();
  }

  /**
   * Initialize all database collections
   */
  initializeDatabases() {
    this.logger.start('Initializing databases');

    // Apps database - stores installed app information
    this.databases.apps = Datastore.create({
      filename: path.join(this.dbPath, 'apps.db'),
      autoload: true,
      timestampData: true
    });

    // Logs database - stores application logs and events
    this.databases.logs = Datastore.create({
      filename: path.join(this.dbPath, 'logs.db'),
      autoload: true,
      timestampData: true
    });

    // Settings database - stores app preferences and configuration
    this.databases.settings = Datastore.create({
      filename: path.join(this.dbPath, 'settings.db'),
      autoload: true,
      timestampData: true
    });

    // Analytics database - stores usage analytics
    this.databases.analytics = Datastore.create({
      filename: path.join(this.dbPath, 'analytics.db'),
      autoload: true,
      timestampData: true
    });

    this.logger.success(`Databases initialized at: ${this.dbPath}`);
    this.setupIndexes();
  }

  /**
   * Setup database indexes for better performance
   */
  async setupIndexes() {
    try {
      // Apps collection indexes
      await this.databases.apps.ensureIndex({ fieldName: 'app_id', unique: true });
      await this.databases.apps.ensureIndex({ fieldName: 'name' });
      await this.databases.apps.ensureIndex({ fieldName: 'category' });
      await this.databases.apps.ensureIndex({ fieldName: 'installed_at' });

      // Logs collection indexes
      await this.databases.logs.ensureIndex({ fieldName: 'type' });
      await this.databases.logs.ensureIndex({ fieldName: 'app_id' });
      await this.databases.logs.ensureIndex({ fieldName: 'createdAt' });

      // Settings collection indexes
      await this.databases.settings.ensureIndex({ fieldName: 'key', unique: true });

      // Analytics collection indexes
      await this.databases.analytics.ensureIndex({ fieldName: 'event_type' });
      await this.databases.analytics.ensureIndex({ fieldName: 'app_id' });
      await this.databases.analytics.ensureIndex({ fieldName: 'timestamp' });

      this.logger.success('Database indexes created');
    } catch (error) {
      this.logger.error('Error setting up indexes:', error);
    }
  }

  /**
   * Get specific database collection
   */
  getCollection(name) {
    if (!this.databases[name]) {
      throw new Error(`Database collection '${name}' not found`);
    }
    return this.databases[name];
  }

  /**
   * Get apps collection
   */
  get apps() {
    return this.databases.apps;
  }

  /**
   * Get logs collection
   */
  get logs() {
    return this.databases.logs;
  }

  /**
   * Get settings collection
   */
  get settings() {
    return this.databases.settings;
  }

  /**
   * Get analytics collection
   */
  get analytics() {
    return this.databases.analytics;
  }

  /**
   * Compact all databases (cleanup and optimize)
   */
  async compactAll() {
    this.logger.start('Compacting databases');
    
    try {
      const collections = Object.keys(this.databases);
      
      for (const collectionName of collections) {
        await this.databases[collectionName].persistence.compactDatafile();
        this.logger.info(`Compacted ${collectionName} database`);
      }
      
      this.logger.success('All databases compacted');
    } catch (error) {
      this.logger.error('Error compacting databases:', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  async getStats() {
    const stats = {};
    
    try {
      for (const [name, db] of Object.entries(this.databases)) {
        const count = await db.count({});
        stats[name] = { count };
      }
      
      return stats;
    } catch (error) {
      this.logger.error('Error getting database stats:', error);
      return {};
    }
  }

  /**
   * Clear all data from a specific collection
   */
  async clearCollection(collectionName) {
    if (!this.databases[collectionName]) {
      throw new Error(`Collection '${collectionName}' not found`);
    }
    
    try {
      const result = await this.databases[collectionName].remove({}, { multi: true });
      this.logger.warn(`Cleared ${result} records from ${collectionName} collection`);
      return result;
    } catch (error) {
      this.logger.error(`Error clearing ${collectionName} collection:`, error);
      throw error;
    }
  }

  /**
   * Backup database to JSON files
   */
  async backup(backupPath) {
    const fs = require('fs');
    const backupData = {};
    
    try {
      for (const [name, db] of Object.entries(this.databases)) {
        const data = await db.find({});
        backupData[name] = data;
      }
      
      const backupFile = path.join(backupPath, `fileduck-backup-${Date.now()}.json`);
      fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));
      
      this.logger.success(`Database backup created: ${backupFile}`);
      return backupFile;
    } catch (error) {
      this.logger.error('Error creating backup:', error);
      throw error;
    }
  }

  /**
   * Close all database connections
   */
  async close() {
    this.logger.info('Closing database connections');
    
    // NeDB doesn't require explicit closing, but we can compact before shutdown
    try {
      await this.compactAll();
      this.logger.success('Database connections closed');
    } catch (error) {
      this.logger.error('Error closing databases:', error);
    }
  }
}

// Create singleton instance
let dbInstance = null;

/**
 * Get database instance (singleton)
 */
function getDatabase() {
  if (!dbInstance) {
    dbInstance = new Database();
  }
  return dbInstance;
}

module.exports = {
  Database,
  getDatabase
};
