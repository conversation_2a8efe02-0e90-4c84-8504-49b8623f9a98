# FileDuck Database Schema Documentation

This document describes the expected schema for each NeDB collection in FileDuck. While NeDB is schema-less, these definitions serve as documentation and validation guidelines.

## Apps Collection (`apps.db`)

Stores information about installed applications.

```json
{
  "_id": "auto-generated-id",
  "app_id": "pdf-converter-1",
  "name": "PDF Converter Pro",
  "description": "Convert files to PDF format easily",
  "version": "1.0.0",
  "category": "Productivity",
  "sub_category": "Document Tools",
  "installer_url": "http://127.0.0.1:8000/api/playstore/apps/v1/json-to-csv/installer-file-download",
  "local_path": "/path/to/installed/app",
  "language": "python",
  "requirements": ["pandas", "numpy"],
  "installed_at": "2024-01-15T10:30:00.000Z",
  "last_run_at": "2024-01-15T14:20:00.000Z",
  "run_count": 5,
  "source": "local",
  "destination": "pdf",
  "format": "config.json",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T14:20:00.000Z"
}
```

### Required Fields:
- `app_id`: Unique identifier for the app
- `name`: Display name of the app
- `version`: App version
- `installed_at`: Installation timestamp

### Optional Fields:
- `description`: App description
- `category`: Primary category
- `sub_category`: Secondary category
- `installer_url`: Original download URL
- `local_path`: Path to installed files
- `language`: Runtime language (python, shell, etc.)
- `requirements`: Array of dependencies
- `last_run_at`: Last execution timestamp
- `run_count`: Number of times app was executed
- `source`: Source format type
- `destination`: Destination format type
- `format`: Configuration format used

## Logs Collection (`logs.db`)

Stores application logs and events.

```json
{
  "_id": "auto-generated-id",
  "type": "app_install",
  "level": "info",
  "message": "App installed successfully",
  "app_id": "pdf-converter-1",
  "details": {
    "duration": 1500,
    "file_size": 2048576,
    "additional_data": "any"
  },
  "user_id": "user_test_123",
  "session_id": "session-uuid",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

### Log Types:
- `app_install`: App installation events
- `app_uninstall`: App uninstallation events
- `app_run`: App execution events
- `app_error`: App error events
- `system_error`: System-level errors
- `user_action`: User interaction events

### Log Levels:
- `debug`: Debug information
- `info`: General information
- `warn`: Warning messages
- `error`: Error messages
- `fatal`: Fatal errors

## Settings Collection (`settings.db`)

Stores application preferences and configuration.

```json
{
  "_id": "auto-generated-id",
  "key": "user_preferences",
  "value": {
    "theme": "light",
    "auto_update": true,
    "analytics_enabled": true,
    "default_download_path": "/Users/<USER>/Downloads"
  },
  "type": "object",
  "description": "User preference settings",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

### Setting Types:
- `string`: Text values
- `number`: Numeric values
- `boolean`: True/false values
- `object`: Complex objects
- `array`: Array values

### Common Setting Keys:
- `user_preferences`: User UI preferences
- `app_config`: Application configuration
- `api_settings`: API endpoints and keys
- `cache_settings`: Cache configuration
- `debug_settings`: Debug mode settings

## Analytics Collection (`analytics.db`)

Stores usage analytics and metrics.

```json
{
  "_id": "auto-generated-id",
  "event_type": "app_run",
  "app_id": "pdf-converter-1",
  "user_id": "user_test_123",
  "session_id": "session-uuid",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "properties": {
    "file_count": 3,
    "processing_time": 2500,
    "success": true,
    "error_code": null
  },
  "metadata": {
    "platform": "darwin",
    "app_version": "0.1.0",
    "electron_version": "22.0.0"
  },
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

### Event Types:
- `app_install`: App installation
- `app_uninstall`: App uninstallation
- `app_run`: App execution
- `app_error`: App errors
- `user_login`: User authentication
- `feature_usage`: Feature utilization
- `performance_metric`: Performance data

## Indexes

The following indexes are automatically created for performance:

### Apps Collection:
- `app_id` (unique)
- `name`
- `category`
- `installed_at`

### Logs Collection:
- `type`
- `app_id`
- `createdAt`

### Settings Collection:
- `key` (unique)

### Analytics Collection:
- `event_type`
- `app_id`
- `timestamp`

## Data Validation

While NeDB doesn't enforce schemas, the application should validate data before insertion:

1. **Required fields**: Ensure all required fields are present
2. **Data types**: Validate field types match expected schema
3. **Constraints**: Check unique constraints and relationships
4. **Sanitization**: Clean and sanitize input data

## Migration Strategy

When schema changes are needed:

1. **Backward compatibility**: Ensure new code can handle old data formats
2. **Migration scripts**: Create scripts to update existing data
3. **Version tracking**: Track schema versions in settings collection
4. **Gradual rollout**: Update data incrementally to avoid performance issues
