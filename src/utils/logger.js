// Browser-compatible logger for React frontend
// Uses console with Gen-Z emojis and styling

class BrowserLogger {
  constructor(scope = '') {
    this.scope = scope;
    this.isElectron = typeof window !== 'undefined' && window.electronAPI !== undefined;
  }

  async _formatMessage(emoji, label, message, ...args) {
    const timestamp = new Date().toLocaleTimeString();
    const scopeStr = this.scope ? `[${this.scope}]` : '';
    const prefix = `${scopeStr} › ${emoji} ${label}`;

    // Log to browser console
    if (typeof message === 'object') {
      console.log(`${prefix}`, message, ...args);
    } else {
      console.log(`${prefix} ${message}`, ...args);
    }

    // Also send to Electron main process for server-side logging
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.logMessage({
          level: label,
          scope: this.scope,
          message: typeof message === 'string' ? message : JSON.stringify(message),
          args,
        });
      } catch (error) {
        // Fallback to console if Electron IPC fails
        console.log(`⚠️ warn Failed to send log to main process:`, error);
      }
    }
  }

  // Success and positive actions
  async success(message, ...args) {
    await this._formatMessage('✨', 'success', message, ...args);
  }

  async complete(message, ...args) {
    await this._formatMessage('🎉', 'complete', message, ...args);
  }

  // Information and status
  async info(message, ...args) {
    await this._formatMessage('💡', 'info', message, ...args);
  }

  async note(message, ...args) {
    await this._formatMessage('📝', 'note', message, ...args);
  }

  // Process and loading states
  async pending(message, ...args) {
    await this._formatMessage('⏳', 'pending', message, ...args);
  }

  async await(message, ...args) {
    await this._formatMessage('⌛', 'await', message, ...args);
  }

  async watch(message, ...args) {
    await this._formatMessage('👀', 'watch', message, ...args);
  }

  async start(message, ...args) {
    await this._formatMessage('🚀', 'start', message, ...args);
  }

  // Warnings and cautions
  async warn(message, ...args) {
    await this._formatMessage('⚠️', 'warn', message, ...args);
  }

  async pause(message, ...args) {
    await this._formatMessage('⏸️', 'pause', message, ...args);
  }

  // Errors and failures
  async error(message, ...args) {
    await this._formatMessage('💥', 'error', message, ...args);
  }

  async fatal(message, ...args) {
    await this._formatMessage('💀', 'fatal', message, ...args);
  }

  // Debug and development
  async debug(message, ...args) {
    await this._formatMessage('🐛', 'debug', message, ...args);
  }

  // Special FileDuck specific loggers
  async fileduck(message, ...args) {
    await this._formatMessage('🦆', 'fileduck', message, ...args);
  }

  async app(message, ...args) {
    await this._formatMessage('📱', 'app', message, ...args);
  }

  async install(message, ...args) {
    await this._formatMessage('📦', 'install', message, ...args);
  }

  async uninstall(message, ...args) {
    await this._formatMessage('🗑️', 'uninstall', message, ...args);
  }

  async python(message, ...args) {
    await this._formatMessage('🐍', 'python', message, ...args);
  }

  async file(message, ...args) {
    await this._formatMessage('📄', 'file', message, ...args);
  }

  async api(message, ...args) {
    await this._formatMessage('🌐', 'api', message, ...args);
  }

  async database(message, ...args) {
    await this._formatMessage('🗄️', 'database', message, ...args);
  }

  // Create scoped logger
  scope(scopeName) {
    return new BrowserLogger(scopeName);
  }
}

// Create the main logger instance
const logger = new BrowserLogger();

// Export both the configured logger and the class for custom instances
export {
  logger,
  BrowserLogger
};

export const createLogger = (scope) => logger.scope(scope);
