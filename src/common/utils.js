const _ = require('lodash');

/**
 * Generic utility functions
 * Provides common helper functions used across the application
 */
class Utils {
  /**
   * Generate utility ID from name and version
   */
  static generateUtilityId(name, version) {
    return `${name.toLowerCase().replace(/\s+/g, '-')}-v${version}`;
  }

  /**
   * Sanitize filename for safe file system usage
   */
  static sanitizeFilename(filename) {
    return filename
      .replace(/[^a-z0-9.-]/gi, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();
  }

  /**
   * Deep clone object
   */
  static deepClone(obj) {
    return _.cloneDeep(obj);
  }

  /**
   * Check if object is empty
   */
  static isEmpty(obj) {
    return _.isEmpty(obj);
  }

  /**
   * Merge objects deeply
   */
  static deepMerge(target, ...sources) {
    return _.merge(target, ...sources);
  }

  /**
   * Get nested property safely
   */
  static get(obj, path, defaultValue) {
    return _.get(obj, path, defaultValue);
  }

  /**
   * Set nested property safely
   */
  static set(obj, path, value) {
    return _.set(obj, path, value);
  }

  /**
   * Debounce function
   */
  static debounce(func, wait) {
    return _.debounce(func, wait);
  }

  /**
   * Throttle function
   */
  static throttle(func, wait) {
    return _.throttle(func, wait);
  }

  /**
   * Generate random string
   */
  static generateRandomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Sleep/delay function
   */
  static sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Retry function with exponential backoff
   */
  static async retry(fn, maxAttempts = 3, delay = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts) {
          throw lastError;
        }
        
        // Exponential backoff
        const waitTime = delay * Math.pow(2, attempt - 1);
        await this.sleep(waitTime);
      }
    }
  }

  /**
   * Format bytes to human readable string
   */
  static formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  /**
   * Validate email format
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate URL format
   */
  static isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Truncate string with ellipsis
   */
  static truncate(str, length = 50) {
    return _.truncate(str, { length });
  }

  /**
   * Capitalize first letter
   */
  static capitalize(str) {
    return _.capitalize(str);
  }

  /**
   * Convert string to camelCase
   */
  static camelCase(str) {
    return _.camelCase(str);
  }

  /**
   * Convert string to kebab-case
   */
  static kebabCase(str) {
    return _.kebabCase(str);
  }

  /**
   * Remove duplicates from array
   */
  static uniq(array) {
    return _.uniq(array);
  }

  /**
   * Group array by property
   */
  static groupBy(array, property) {
    return _.groupBy(array, property);
  }

  /**
   * Sort array by property
   */
  static sortBy(array, property) {
    return _.sortBy(array, property);
  }

  /**
   * Pick specific properties from object
   */
  static pick(obj, properties) {
    return _.pick(obj, properties);
  }

  /**
   * Omit specific properties from object
   */
  static omit(obj, properties) {
    return _.omit(obj, properties);
  }

  /**
   * Check if value is a valid JSON string
   */
  static isValidJson(str) {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Parse JSON safely
   */
  static parseJson(str, defaultValue = null) {
    try {
      return JSON.parse(str);
    } catch {
      return defaultValue;
    }
  }

  /**
   * Stringify JSON safely
   */
  static stringifyJson(obj, space = 2) {
    try {
      return JSON.stringify(obj, null, space);
    } catch {
      return null;
    }
  }
}

module.exports = Utils;
