/**
 * Date formatting utilities
 * Provides consistent date formatting across the application
 */
class DateUtils {
  /**
   * Format date to ISO string
   */
  static toISOString(date = new Date()) {
    return date.toISOString();
  }

  /**
   * Format date for display (human readable)
   */
  static formatForDisplay(date) {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Format date for file names (safe characters only)
   */
  static formatForFileName(date = new Date()) {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    return date.toISOString()
      .replace(/:/g, '-')
      .replace(/\./g, '-')
      .slice(0, 19); // Remove milliseconds and timezone
  }

  /**
   * Get relative time (e.g., "2 hours ago")
   */
  static getRelativeTime(date) {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    }
    
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }
    
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
    }
    
    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
  }

  /**
   * Check if date is today
   */
  static isToday(date) {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  /**
   * Check if date is this week
   */
  static isThisWeek(date) {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    return date >= weekAgo && date <= now;
  }

  /**
   * Get timestamp for logging
   */
  static getTimestamp() {
    return new Date().toISOString();
  }

  /**
   * Parse date safely
   */
  static parseDate(dateString) {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return null;
      }
      return date;
    } catch (error) {
      return null;
    }
  }
}

module.exports = DateUtils;
