const { spawn } = require('child_process');
const signale = require('signale');

/**
 * Python execution helper using spawn
 * Handles Python subprocess execution and dependency management
 */
class SpawnPython {
  constructor() {
    this.logger = signale.scope('SpawnPython');
    this.installedPackages = new Set();
  }

  /**
   * Execute Python script with arguments
   */
  async executePython(scriptPath, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      this.logger.info(`Executing Python script: ${scriptPath}`);
      
      const python = spawn('python3', [scriptPath, ...args], {
        cwd: options.cwd || process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      python.stdout.on('data', (data) => {
        const text = data.toString();
        stdout += text;
        this.logger.info(`Python stdout: ${text.trim()}`);
      });

      python.stderr.on('data', (data) => {
        const text = data.toString();
        stderr += text;
        this.logger.warn(`Python stderr: ${text.trim()}`);
      });

      python.on('close', (code) => {
        if (code === 0) {
          this.logger.success(`Python script completed successfully`);
          resolve({ stdout, stderr, code });
        } else {
          this.logger.error(`Python script failed with code ${code}`);
          reject(new Error(`Python execution failed: ${stderr}`));
        }
      });

      python.on('error', (error) => {
        this.logger.error(`Python spawn error:`, error);
        reject(error);
      });

      // Handle input if provided
      if (options.input) {
        python.stdin.write(options.input);
        python.stdin.end();
      }
    });
  }

  /**
   * Install Python package using pip
   */
  async installPackage(packageName) {
    if (this.installedPackages.has(packageName)) {
      this.logger.info(`Package ${packageName} already installed, skipping`);
      return true;
    }

    return new Promise((resolve, reject) => {
      this.logger.info(`Installing Python package: ${packageName}`);
      
      const pip = spawn('pip3', ['install', packageName]);
      let output = '';
      let error = '';

      pip.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        this.logger.info(`pip stdout: ${text.trim()}`);
      });

      pip.stderr.on('data', (data) => {
        const text = data.toString();
        error += text;
        this.logger.warn(`pip stderr: ${text.trim()}`);
      });

      pip.on('close', (code) => {
        if (code === 0) {
          this.installedPackages.add(packageName);
          this.logger.success(`Successfully installed package: ${packageName}`);
          resolve(true);
        } else {
          this.logger.error(`Failed to install package ${packageName}: ${error}`);
          reject(new Error(`pip install failed: ${error}`));
        }
      });
    });
  }

  /**
   * Install multiple dependencies
   */
  async installDependencies(requirements) {
    if (!requirements || !Array.isArray(requirements)) {
      this.logger.info('No requirements to install');
      return;
    }

    this.logger.info(`Installing ${requirements.length} dependencies: ${requirements.join(', ')}`);
    
    for (const packageName of requirements) {
      try {
        await this.installPackage(packageName);
      } catch (error) {
        this.logger.error(`Failed to install ${packageName}: ${error.message}`);
        // Continue with other packages even if one fails
      }
    }
  }

  /**
   * Check if Python is available
   */
  async checkPythonAvailable() {
    try {
      const result = await this.executePython('-c', ['print("Python is available")']);
      return true;
    } catch (error) {
      this.logger.error('Python is not available:', error.message);
      return false;
    }
  }

  /**
   * Get Python version
   */
  async getPythonVersion() {
    try {
      const result = await this.executePython('--version');
      return result.stdout.trim();
    } catch (error) {
      this.logger.error('Failed to get Python version:', error.message);
      return null;
    }
  }
}

module.exports = SpawnPython;
