const signale = require('signale');

/**
 * Centralized logging utility using Signale
 * Provides consistent logging across the application with Gen-Z emojis
 */
class Logger {
  constructor(scope = 'FileDuck') {
    // Configure signale with custom options
    this.logger = signale.scope(scope);
    
    // Custom signale configuration for FileDuck
    signale.config({
      displayFilename: false,
      displayTimestamp: true,
      displayDate: false
    });
  }

  /**
   * Info level logging
   */
  info(message, ...args) {
    this.logger.info(`💡 ${message}`, ...args);
  }

  /**
   * Success level logging
   */
  success(message, ...args) {
    this.logger.success(`✨ ${message}`, ...args);
  }

  /**
   * Warning level logging
   */
  warn(message, ...args) {
    this.logger.warn(`⚠️ ${message}`, ...args);
  }

  /**
   * Error level logging
   */
  error(message, ...args) {
    this.logger.error(`💥 ${message}`, ...args);
  }

  /**
   * Debug level logging
   */
  debug(message, ...args) {
    this.logger.debug(`🔍 ${message}`, ...args);
  }

  /**
   * Start operation logging
   */
  start(message, ...args) {
    this.logger.start(`🚀 ${message}`, ...args);
  }

  /**
   * Complete operation logging
   */
  complete(message, ...args) {
    this.logger.complete(`🎉 ${message}`, ...args);
  }

  /**
   * Pending operation logging
   */
  pending(message, ...args) {
    this.logger.pending(`⏳ ${message}`, ...args);
  }

  /**
   * Note level logging
   */
  note(message, ...args) {
    this.logger.note(`📝 ${message}`, ...args);
  }

  /**
   * Installation specific logging
   */
  install(message, ...args) {
    this.logger.info(`📦 ${message}`, ...args);
  }

  /**
   * Download specific logging
   */
  download(message, ...args) {
    this.logger.info(`⬇️ ${message}`, ...args);
  }

  /**
   * Database specific logging
   */
  database(message, ...args) {
    this.logger.info(`🗄️ ${message}`, ...args);
  }

  /**
   * API specific logging
   */
  api(message, ...args) {
    this.logger.info(`🌐 ${message}`, ...args);
  }

  /**
   * File operation specific logging
   */
  file(message, ...args) {
    this.logger.info(`📁 ${message}`, ...args);
  }

  /**
   * Python specific logging
   */
  python(message, ...args) {
    this.logger.info(`🐍 ${message}`, ...args);
  }

  /**
   * Create a scoped logger
   */
  scope(scopeName) {
    return new Logger(scopeName);
  }

  /**
   * Log with custom emoji
   */
  custom(emoji, message, ...args) {
    this.logger.info(`${emoji} ${message}`, ...args);
  }
}

// Create default logger instance
const defaultLogger = new Logger();

// Export both class and default instance
module.exports = {
  Logger,
  log: defaultLogger
};
