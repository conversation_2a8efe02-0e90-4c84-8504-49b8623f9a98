
# 🦆 FileDuck Refactor Instructions for Cursor AI

## 🧠 Goal

Refactor this ElectronJS project to be clean, modular, and production-ready using **NeDB** for data storage. Focus on readability, separation of concerns, and maintainability for a solo indie dev workflow.

---

## 🗂️ Folder Structure

Restructure the codebase into the following layout:

```
src/
├── common/               # Shared reusable logic
│   ├── fileOps.js        # File I/O helpers, like zip and all and others
│   ├── spawnPython.js    # Python execution helpers
│   ├── dateUtils.js      # Date formatting utilities
│   ├── log.js            # Pretty logger using chalk + log-symbols
│   └── utils.js          # Any generic functions
│
├── data/                 # All database logic
│   ├── db.js             # NeDB store initialization
│   ├── schema.md         # Describe schema expectations (self-imposed, not enforced)
│   └── models/           # Logical grouping of CRUD per collection
│       ├── logs.js       # Log operations (insert, read, etc.)
│       └── settings.js   # App preferences and state
```

---

## 📦 NeDB Integration

- NeDB should be installed first. NeDB will be used for tracking app installation, uninstallation, run events etc.
- in contextBridge.js - in installUtility(downloadUrl) - we dont check the file path for if exists or not - we check the app_id in database. you will get this api response from all api in tool store page:

{
"_id": {
"$oid": "6855916926596c27e439b7c7"
},
"app_id": "pdf-converter-1",
"name": "PDF Converter Pro",
"description": "Convert files to PDF format easily",
"source": "local",
"destination": "pdf",
"version": "1.0.0",
"installer_url": "http://127.0.0.1:8000/api/playstore/apps/v1/json-to-csv/installer-file-download",
"category": "Productivity",
"sub_category": "Document Tools",
"search_tags": [
"pdf",
"convert",
"document",
"productivity"
],
"install": 1502,
"uninstall": 50,
"run_count": 3000,
"rating_count": 121,
"rating_sum": 485,
"average_rating": 4.01,
"created_at": {
"$date": "2025-06-20T16:50:49.006Z"
}
}

-- you will store the app id, name, description, when installed, how many run etc. in database. this is from where we track our apps.
-- in tool store after you receive an api response, you check if the app is installed or not. if installed, you show the open button. if not, you show the get button.


- Use `nedb-promises` for async/await-style usage.
- Store all `.db` files inside the user's app data path:
  ```js
  const dbPath = app.getPath('userData');
  ```

- Each collection (like `logs.db`, `settings.db`) should have its own model file for CRUD logic.
- Export clean functions: `createLog()`, `getAllLogs()`, `deleteLog(id)` etc.

---

## ✅ Schema Definitions (Optional)

While NeDB is schema-less, define expected fields per collection in the respective model or in `data/schema.md` as documentation:

```json
// logs
{
  "type": "conversion",
  "status": "success",
  "fileName": "example.json",
  "createdAt": "Date"
}
```

Add internal checks if needed before inserts to enforce consistency.

---

## 🛠 Reusable Logic

Move all shared utilities to the `common/` folder:

- `fileOps.js` → read/write/delete file helpers
- `spawnPython.js` → spawn Python subprocesses cleanly
- `dateUtils.js` → date formatting for logs/UI
- `log.js` → colored console logs using `chalk` + `log-symbols`

This ensures clean reusability across modules.

---

## 💡 Final Notes

- All DB access should go through `data/models/*.js` — no raw NeDB usage outside.
- Imports should be clean and modular. No circular deps.
- Focus on readability and indie-scale simplicity.

🧠 This structure allows rapid feature addition, minimal bugs, and easy future migration (e.g., to SQLite, or sync to cloud).
